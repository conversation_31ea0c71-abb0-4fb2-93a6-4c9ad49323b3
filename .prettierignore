# 忽略目录
node_modules
dist
lib
coverage
.nyc_output
.cache
.temp
.tmp

# 忽略文件
*.log
*.lock
package-lock.json
yarn.lock
pnpm-lock.yaml

# 忽略配置文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 忽略构建产物
build
out
.next
.nuxt
.vuepress/dist

# 忽略文档
CHANGELOG.md
LICENSE
*.md

# 忽略特定文件类型
*.min.js
*.min.css
*.bundle.js
*.chunk.js

# 忽略版本控制
.git
.svn
.hg

# 忽略编辑器配置
.vscode
.idea
*.swp
*.swo

# 忽略操作系统文件
.DS_Store
Thumbs.db
