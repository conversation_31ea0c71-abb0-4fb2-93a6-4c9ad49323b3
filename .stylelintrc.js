module.exports = {
  extends: ['stylelint-config-standard'],
  rules: {
    // 允许未知的伪类选择器
    'selector-pseudo-class-no-unknown': [
      true,
      {
        ignorePseudoClasses: ['global'],
      },
    ],
    // 允许未知的伪元素选择器
    'selector-pseudo-element-no-unknown': [
      true,
      {
        ignorePseudoElements: ['v-deep'],
      },
    ],
    // 允许未知的@规则
    'at-rule-no-unknown': [
      true,
      {
        ignoreAtRules: [
          'tailwind',
          'apply',
          'variants',
          'responsive',
          'screen',
          'function',
          'if',
          'each',
          'include',
          'mixin',
        ],
      },
    ],
    // 允许空源码
    'no-empty-source': null,
    // 允许未知的单位
    'unit-no-unknown': [
      true,
      {
        ignoreUnits: ['rpx'],
      },
    ],
    // 禁止低优先级的选择器出现在高优先级的选择器之后
    'no-descending-specificity': null,
    // 禁止在具有较高优先级的选择器后出现被其覆盖的较低优先级的选择器
    'no-duplicate-selectors': null,
    // 兼容自定义标签名
    'selector-type-no-unknown': [
      true,
      {
        ignoreTypes: ['page'],
      },
    ],
    // 允许 styled-components 的模板字符串和 CSS-in-JS
    'value-keyword-case': null,
    // 允许现代颜色函数
    'color-function-notation': null,
    'alpha-value-notation': null,
    'color-hex-length': null,
    'hue-degree-notation': null,
    'color-function-alias-notation': null,
    // 允许一些常见的样式模式
    'declaration-empty-line-before': null,
    'rule-empty-line-before': null,
    'declaration-block-no-duplicate-properties': null,
    'custom-property-pattern': null,
    'length-zero-no-unit': null,
  },
  ignoreFiles: ['**/*.js', '**/*.jsx', '**/*.tsx', '**/*.ts', '**/*.json', '**/*.md', '**/*.yaml'],
};
