module.exports = {
  // 对 js、jsx、ts、tsx 文件执行 eslint 和 prettier
  '*.{js,jsx,ts,tsx}': [
    'eslint --fix',
    'prettier --write',
  ],
  // 对 css、less、scss 文件执行 stylelint 和 prettier
  '*.{css,less,scss}': [
    'stylelint --fix',
    'prettier --write',
  ],
  // 对 json、md 文件执行 prettier
  '*.{json,md}': [
    'prettier --write',
  ],
  // 对 vue 文件执行 eslint、stylelint 和 prettier
  '*.vue': [
    'eslint --fix',
    'stylelint --fix',
    'prettier --write',
  ],
};
