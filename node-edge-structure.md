# 节点和边的数据结构文档

## Node 节点属性

### WorkflowNodeJSON 接口

```typescript
interface WorkflowNodeJSON {
  id: string; // 节点唯一标识符
  type: string | number; // 节点类型
  meta?: WorkflowNodeMeta; // UI 数据
  data?: any; // 表单数据
  blocks?: WorkflowNodeJSON[]; // 子节点
  edges?: WorkflowEdgeJSON[]; // 子节点间连线
}
```

### 自定义 FlowNodeJSON 接口

```typescript
interface FlowNodeJSON extends WorkflowNodeJSON {
  data: {
    title?: string; // 节点标题
    inputsValues?: Record<string, IFlowValue>; // 输入数据值
    inputs?: JsonSchema; // 通过 JsonSchema 定义节点的输入数据
    outputs?: JsonSchema; // 通过 JsonSchema 定义节点的输出数据
    [key: string]: any; // 其他属性
  };
}
```

```typescript
interface JsonSchema<T = string> {
  type?: T;
  default?: any;
  title?: string;
  description?: string;
  enum?: (string | number)[];
  properties?: Record<string, IJsonSchema<T>>;
  additionalProperties?: IJsonSchema<T>;
  items?: IJsonSchema<T>;
  required?: string[];
  $ref?: string;
  extra?: {
    index?: number;
    weak?: boolean;
    formComponent?: string;
    [key: string]: any;
  };
}
```

### WorkflowNodeMeta 接口（UI 数据）

```typescript
interface WorkflowNodeMeta extends FlowNodeMeta {
  position?: IPoint; // 节点位置坐标
  canvasPosition?: IPoint; // 画布位置坐标
  deleteDisable?: boolean; // 禁用删除
  copyDisable?: boolean; // 禁用复制
  inputDisable?: boolean; // 禁用输入
  outputDisable?: boolean; // 禁用输出
  defaultPorts?: WorkflowPorts; // 默认端口配置
  useDynamicPort?: boolean; // 使用动态端口
  subCanvas?: (node: WorkflowNodeEntity) => WorkflowSubCanvas | undefined; // 子画布
  isContainer?: boolean; // 是否为容器节点
}
```

### FlowNodeMeta 基础接口（节点渲染相关配置）

```typescript
interface FlowNodeMeta {
  isStart?: boolean; // 是否为开始节点
  addable?: boolean; // 是否可添加
  expandable?: boolean; // 是否可展开
  draggable?: boolean | ((node: FlowNodeEntity) => boolean); // 是否可拖拽
  selectable?:
    | boolean
    | ((node: FlowNodeEntity, mousePos?: PositionSchema) => boolean); // 是否可选择
  deleteDisable?: boolean; // 禁用删除
  copyDisable?: boolean; // 禁用复制
  addDisable?: boolean; // 禁用添加
  hidden?: boolean; // 是否隐藏
  size?: SizeSchema; // 节点大小
  origin?: OriginSchema; // 原点位置（已废弃）
  defaultExpanded?: boolean; // 默认展开状态
  defaultCollapsed?: boolean; // 默认折叠状态
  expandedSize?: SizeSchema; // 展开后的大小
  spacing?: number | ((transform: FlowNodeTransformData) => number); // 间距
  padding?:
    | PaddingSchema
    | ((transform: FlowNodeTransformData) => PaddingSchema); // 内边距
  inlineSpacingPre?: number | ((transform: FlowNodeTransformData) => number); // 前置内联间距
}
```

### 自定义 FlowNodeMeta 扩展

```typescript
interface FlowNodeMeta extends WorkflowNodeMeta {
  disableSideBar?: boolean; // 禁用侧边栏
}
```

## Node 类型

### WorkflowNodeType 枚举

```typescript
enum WorkflowNodeType {
  Start = "start", // 开始节点
  End = "end", // 结束节点
  LLM = "llm", // LLM 节点
  Condition = "condition", // 条件节点
  Loop = "loop", // 循环节点
  Comment = "comment", // 注释节点
}
```

## Meta 属性的作用

Meta 属性主要用于控制节点在画布上的 UI 行为和渲染配置：

1. **位置控制**: `position`, `canvasPosition` - 控制节点在画布上的位置
2. **交互控制**: `deleteDisable`, `copyDisable`, `draggable`, `selectable` - 控制节点的交互行为
3. **端口配置**: `defaultPorts`, `useDynamicPort` - 配置节点的输入输出端口
4. **显示控制**: `hidden`, `size`, `expandable` - 控制节点的显示状态和大小
5. **布局控制**: `spacing`, `padding` - 控制节点的布局间距
6. **特殊功能**: `isStart`, `isContainer`, `subCanvas` - 标识节点的特殊功能

## WorkflowPorts 端口配置

### WorkflowPort 接口

```typescript
interface WorkflowPort {
  portID?: string | number; // 端口ID，没有代表默认连接点
  disabled?: boolean; // 禁用端口
  targetElement?: HTMLElement; // 将点位渲染到该父节点上
  type: WorkflowPortType; // 输入或者输出点
}

type WorkflowPortType = "input" | "output";
type WorkflowPorts = WorkflowPort[];
```

## Edge 边属性

### WorkflowEdgeJSON 接口

```typescript
interface WorkflowEdgeJSON {
  sourceNodeID: string; // 源节点ID
  targetNodeID: string; // 目标节点ID
  sourcePortID?: string | number; // 源端口ID（可选）
  targetPortID?: string | number; // 目标端口ID（可选）
}
```

## 画布数据结构

### FlowDocumentJSON 接口（最终画布生成的数据结构）

```typescript
interface FlowDocumentJSON {
  nodes: FlowNodeJSON[]; // 节点数组
  edges: WorkflowEdgeJSON[]; // 边数组
}
```

## 数据结构示例

### 节点示例

```typescript
const nodeExample: WorkflowNodeJSON = {
  id: "start_0",
  type: "start",
  meta: {
    position: {
      x: 180,
      y: 381.75,
    },
    deleteDisable: true,
    copyDisable: true,
    defaultPorts: [{ type: "output" }],
    size: {
      width: 360,
      height: 211,
    },
  },
  data: {
    title: "Start",
    outputs: {
      type: "object",
      properties: {
        query: {
          type: "string",
          default: "Hello Flow.",
        },
        enable: {
          type: "boolean",
          default: true,
        },
      },
    },
  },
};
```

### 边示例

```typescript
const edgeExample: WorkflowEdgeJSON = {
  sourceNodeID: "start_0",
  targetNodeID: "llm_1",
  sourcePortID: "output",
  targetPortID: "input",
};
```

### 完整画布数据示例

```typescript
const workflowExample: WorkflowJSON = {
  nodes: [
    // 节点数组
    {
      id: "start_0",
      type: "start",
      meta: {
        /* meta 配置 */
      },
      data: {
        /* 业务数据 */
      },
    },
    // ... 更多节点
  ],
  edges: [
    // 边数组
    {
      sourceNodeID: "start_0",
      targetNodeID: "llm_1",
    },
    // ... 更多边
  ],
};
```

## 注意事项

1. **节点 ID**: 每个节点必须有唯一的 `id` 标识符
2. **节点类型**: `type` 字段决定了节点的行为和渲染方式
3. **端口连接**: 边通过 `sourceNodeID` 和 `targetNodeID` 连接节点，可选的 `portID` 指定具体的端口
4. **Meta 配置**: Meta 属性主要影响 UI 行为，不影响业务逻辑
5. **数据扩展**: `data` 字段可以包含任意业务数据，通过 JsonSchema 定义输入输出结构
