.workflow-comment {
    width: auto;
    height: auto;
    min-width: 120px;
    min-height: 80px;
}

.workflow-comment-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    width: 100%;
    height: 100%;
    border-radius: 8px;
    outline: 1px solid;
    padding: 6px 2px 6px 10px;
    overflow: hidden;
}

.workflow-comment-drag-area {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: move;
}

.workflow-comment-content-drag-area {
    position: absolute;
    height: 100%;
    width: calc(100% - 22px);
}

.workflow-comment-resize-area {
    position: absolute;
    width: 10px;
    height: 10px;
}

.workflow-comment-editor {
    width: 100%;
    height: 100%;
}

.workflow-comment-editor-placeholder {
    margin: 0;
    position: absolute;
    pointer-events: none;
    color: rgba(55, 67, 106, 0.38);
    font-weight: 500;
}

.workflow-comment-editor-textarea {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    appearance: none;
    border: none;
    margin: 0;
    padding: 0;
    width: 100%;
    background: none;
    color: inherit;
    font-family: inherit;
    font-size: 16px;
    resize: none;
    outline: none;
}

.workflow-comment-more-button {
    position: absolute;
    right: 6px;
}

.workflow-comment-more-button > .semi-button {
    color: rgba(255, 255, 255, 0);
    background: none;
}

.workflow-comment-more-button > .semi-button:hover {
    color: #ffa100;
    background: #fbf2d2cc;
    backdrop-filter: blur(1px);
}

.workflow-comment-more-button-focused > .semi-button:hover {
    color: #ff811a;
    background: #ffe3cecc;
    backdrop-filter: blur(1px);
}

.workflow-comment-more-button > .semi-button:active {
    color: #f2b600;
    background: #ede5c7cc;
    backdrop-filter: blur(1px);
}

.workflow-comment-more-button-focused > .semi-button:active {
    color: #ff811a;
    background: #eed5c1cc;
    backdrop-filter: blur(1px);
}
